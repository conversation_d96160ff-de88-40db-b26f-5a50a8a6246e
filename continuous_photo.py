#####################################################################################################
# @file         continuous_photo.py
# <AUTHOR> - Alex (基于正点原子代码优化)
# @version      V2.0
# @date         2025-01-30
# @brief        K230D连续拍照实验 - 支持连续保存不覆盖
# @note         按下KEY0拍摄JPG格式的照片，按下KEY1拍摄BMP格式的照片
#               每张照片都有唯一的时间戳文件名，不会覆盖
# @license      Copyright (c) 2025, 米醋电子工作室
#####################################################################################################
# @attention
#
# 实验平台:正点原子 K230D BOX开发板
# 功能优化:连续拍照保存，自动生成唯一文件名
# 文件命名:photo_YYYYMMDD_HHMMSS_XXX.jpg/bmp
#
#####################################################################################################

import time, os, sys
from machine import Pin
from machine import FPIOA
from media.sensor import *  # 导入sensor模块，使用摄像头相关接口
from media.display import * # 导入display模块，使用display相关接口
from media.media import *   # 导入media模块，使用meida相关接口
import image                # 导入Image模块，使用Image相关接口

class ContinuousPhotoCapture:
    """连续拍照类 - 支持唯一文件名生成"""
    
    def __init__(self):
        self.photo_count = 0        # 照片计数器
        self.jpg_count = 0          # JPG照片计数器
        self.bmp_count = 0          # BMP照片计数器
        self.last_key0_time = 0     # KEY0最后按下时间
        self.last_key1_time = 0     # KEY1最后按下时间
        self.debounce_time = 300    # 按键防抖时间(ms)
        
    def get_timestamp(self):
        """
        获取当前时间戳字符串
        @return: 格式化的时间戳字符串 YYYYMMDD_HHMMSS
        """
        # 获取当前时间 (注意：K230D可能没有RTC，使用运行时间)
        current_time = time.ticks_ms()
        
        # 转换为可读的时间格式 (简化版本)
        # 由于K230D可能没有实时时钟，我们使用运行时间来生成唯一标识
        seconds = current_time // 1000
        minutes = seconds // 60
        hours = minutes // 60
        
        # 格式化时间戳
        timestamp = f"{hours:04d}{minutes%60:02d}{seconds%60:02d}_{current_time%1000:03d}"
        return timestamp
    
    def generate_filename(self, file_type="jpg"):
        """
        生成唯一的文件名
        @param file_type: 文件类型 "jpg" 或 "bmp"
        @return: 唯一的文件名
        """
        timestamp = self.get_timestamp()
        
        if file_type.lower() == "jpg":
            self.jpg_count += 1
            filename = f"photo_JPG_{timestamp}_{self.jpg_count:03d}.jpg"
        else:
            self.bmp_count += 1
            filename = f"photo_BMP_{timestamp}_{self.bmp_count:03d}.bmp"
            
        self.photo_count += 1
        return filename
    
    def save_photo(self, img, file_type="jpg"):
        """
        保存照片到文件
        @param img: 图像对象
        @param file_type: 文件类型
        @return: 保存是否成功
        """
        try:
            filename = self.generate_filename(file_type)
            filepath = f"/data/PHOTO/{filename}"
            
            # 保存图像
            img.save(filepath)
            
            print(f"📸 照片保存成功: {filename}")
            print(f"   文件路径: {filepath}")
            print(f"   总计拍摄: {self.photo_count} 张")
            print(f"   JPG: {self.jpg_count} 张, BMP: {self.bmp_count} 张")
            
            return True
            
        except Exception as e:
            print(f"❌ 照片保存失败: {e}")
            return False
    
    def is_key_pressed_debounced(self, key, key_type):
        """
        带防抖的按键检测
        @param key: 按键对象
        @param key_type: 按键类型 0或1
        @return: 是否为有效按键
        """
        current_time = time.ticks_ms()
        
        if key.value() == 0:  # 按键按下
            if key_type == 0:  # KEY0
                if current_time - self.last_key0_time > self.debounce_time:
                    self.last_key0_time = current_time
                    return True
            else:  # KEY1
                if current_time - self.last_key1_time > self.debounce_time:
                    self.last_key1_time = current_time
                    return True
        
        return False

# 实例化FPIOA
fpioa = FPIOA()

# 为IO分配相应的硬件功能
fpioa.set_function(34, FPIOA.GPIO34)
fpioa.set_function(35, FPIOA.GPIO35)

# 构造GPIO对象
key0 = Pin(34, Pin.IN, pull=Pin.PULL_UP, drive=7)
key1 = Pin(35, Pin.IN, pull=Pin.PULL_UP, drive=7)

# 创建连续拍照对象
photo_capture = ContinuousPhotoCapture()

try:
    # 创建照片保存目录
    try:
        os.mkdir("/data/PHOTO")
        print("📁 创建照片保存目录: /data/PHOTO")
    except Exception:
        print("📁 照片保存目录已存在: /data/PHOTO")
    
    # 初始化摄像头
    sensor = Sensor(width=1280, height=960)  # 构建摄像头对象
    sensor.reset()  # 复位和初始化摄像头

    sensor.set_framesize(Sensor.VGA)       # 设置帧大小VGA(640x480)，默认通道0
    sensor.set_pixformat(Sensor.YUV420SP)  # 设置输出图像格式，默认通道0

    # 将通道0图像绑定到视频输出
    bind_info = sensor.bind_info()
    Display.bind_layer(**bind_info, layer=Display.LAYER_VIDEO1)

    # 设置通道1输出格式，用于图像保存
    sensor.set_framesize(Sensor.SXGAM, chn=CAM_CHN_ID_1)   # 输出帧大小SXGAM(1280x960)
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_1)  # 设置输出图像格式，选择通道1

    # 初始化LCD显示器
    Display.init(Display.ST7701, width=640, height=480, to_ide=False)
    MediaManager.init()  # 初始化media资源管理器

    sensor.run()  # 启动sensor
    
    print("🚀 K230D连续拍照程序启动成功！")
    print("📋 操作说明:")
    print("   KEY0 (Pin34) - 拍摄JPG格式照片")
    print("   KEY1 (Pin35) - 拍摄BMP格式照片")
    print("   每张照片都有唯一文件名，不会覆盖")
    print("⏳ 等待按键操作...")

    while True:
        os.exitpoint()  # 检测IDE中断
        
        # 检测KEY0按下 - 拍摄JPG照片
        if photo_capture.is_key_pressed_debounced(key0, 0):
            print("\n🔵 KEY0按下 - 拍摄JPG照片")
            img = sensor.snapshot(chn=CAM_CHN_ID_1)  # 从通道1捕获一张图
            photo_capture.save_photo(img, "jpg")
            
        # 检测KEY1按下 - 拍摄BMP照片
        if photo_capture.is_key_pressed_debounced(key1, 1):
            print("\n🟡 KEY1按下 - 拍摄BMP照片")
            img = sensor.snapshot(chn=CAM_CHN_ID_1)  # 从通道1捕获一张图
            photo_capture.save_photo(img, "bmp")
        
        time.sleep_ms(10)  # 短暂延时，减少CPU占用

# IDE中断释放资源代码
except KeyboardInterrupt as e:
    print(f"\n⏹️  用户停止程序: {e}")
    print(f"📊 本次拍摄统计: 总计 {photo_capture.photo_count} 张")
    print(f"   JPG: {photo_capture.jpg_count} 张")
    print(f"   BMP: {photo_capture.bmp_count} 张")
except BaseException as e:
    print(f"❌ 程序异常: {e}")
finally:
    # 释放资源
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("🔚 K230D连续拍照程序已退出")
