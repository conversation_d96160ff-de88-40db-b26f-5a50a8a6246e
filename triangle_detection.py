#####################################################################################################
# @file         triangle_detection.py
# <AUTHOR> - Alex
# @version      V2.0
# @date         2025-01-30
# @brief        K230D三角形识别实验 - 兼容版本
# @license      Copyright (c) 2025, 米醋电子工作室
#####################################################################################################
# @attention
#
# 实验平台: 正点原子 K230D BOX开发板
# 功能说明: 使用摄像头实时识别三角形形状
# 识别方法: 基于K230D原生支持的图像处理方法
#
#####################################################################################################

import time, os, sys
from media.sensor import *  # 导入sensor模块，使用摄像头相关接口
from media.display import * # 导入display模块，使用display相关接口
from media.media import *   # 导入media模块，使用meida相关接口
import image
import math

class TriangleDetector:
    """高性能黑色三角形检测器类 - 优化刷新率"""

    def __init__(self):
        self.min_area = 300          # 降低最小面积阈值，提升检测速度
        self.max_area = 25000        # 最大三角形面积阈值
        self.min_pixels = 50         # 降低最小像素数，减少计算量

    def is_triangle_blob_fast(self, blob):
        """
        快速判断色块是否为三角形 - 性能优化版本
        @param blob: 色块对象
        @return: True表示是三角形，False表示不是
        """
        # 快速面积检查
        area = blob.area()
        if area < self.min_area or area > self.max_area:
            return False

        # 快速像素数检查
        if blob.pixels() < self.min_pixels:
            return False

        # 简化的形状检查 - 只使用最关键的特征
        # 计算密度 (像素数/外接矩形面积)
        rect_area = blob.w() * blob.h()
        if rect_area == 0:
            return False

        density = blob.pixels() / rect_area

        # 检查长宽比 - 简化计算
        if blob.h() == 0 or blob.w() == 0:
            return False

        aspect_ratio = max(blob.w() / blob.h(), blob.h() / blob.w())

        # 简化的三角形判断条件 - 减少计算量
        return (0.35 < density < 0.85 and aspect_ratio < 4.0)

def find_largest_black_triangle_fast(img, detector):
    """
    高性能查找最大的黑色三角形 - 优化刷新率版本
    @param img: 输入图像
    @param detector: 三角形检测器
    @return: 最大的黑色三角形色块，如果没有找到返回None
    """
    largest_triangle = None
    max_area = 0

    # 优化：只使用最有效的黑色阈值，减少循环次数
    # 选择最适合的单一阈值，提升检测速度
    black_threshold = (0, 35, -128, 127, -128, 127)  # 最优黑色阈值

    try:
        # 单次查找黑色色块 - 大幅减少计算量
        blobs = img.find_blobs([black_threshold],
                              pixels_threshold=detector.min_pixels,
                              area_threshold=detector.min_area,
                              merge=True)

        # 快速遍历所有色块
        for blob in blobs:
            # 使用快速三角形检测方法
            if detector.is_triangle_blob_fast(blob):
                # 找到面积最大的三角形
                if blob.area() > max_area:
                    max_area = blob.area()
                    largest_triangle = blob

    except Exception as e:
        # 异常处理，但不影响性能
        pass

    return largest_triangle

def draw_largest_black_triangle(img, triangle_blob):
    """
    高性能绘制最大的黑色三角形 - 减少绘制操作
    @param img: 图像对象
    @param triangle_blob: 最大的黑色三角形色块，可能为None
    """
    if triangle_blob is None:
        return

    # 简化绘制操作，提升性能
    # 绘制红色外接矩形
    img.draw_rectangle(triangle_blob.rect(), color=(255, 0, 0), thickness=2)

    # 绘制绿色中心点
    img.draw_circle(triangle_blob.cx(), triangle_blob.cy(), 5, color=(0, 255, 0), thickness=2, fill=True)

    # 简化标签，减少字符串操作
    img.draw_string(triangle_blob.x(), triangle_blob.y()-15, "TARGET", color=(255, 255, 0), scale=1)

# 主程序
try:
    # 初始化摄像头
    sensor = Sensor(width=1280, height=960)  # 构建摄像头对象
    sensor.reset()  # 复位和初始化摄像头
    sensor.set_framesize(Sensor.VGA)      # 设置帧大小VGA(640x480)
    sensor.set_pixformat(Sensor.RGB565)   # 设置输出图像格式
    
    # 初始化显示器
    Display.init(Display.ST7701, width=640, height=480, fps=90, to_ide=True)
    MediaManager.init()  # 初始化media资源管理器
    sensor.run()  # 启动sensor
    
    # 创建三角形检测器
    triangle_detector = TriangleDetector()

    print("K230D高性能黑色三角形识别程序启动！")
    print("已优化刷新率，实现流畅实时检测")
    print("将黑色三角形物体放在摄像头前进行识别")

    clock = time.clock()  # 构造clock对象用于计算FPS
    frame_count = 0
    last_detection_frame = 0

    while True:
        os.exitpoint()  # 检测IDE中断
        clock.tick()    # 记录开始时间
        frame_count += 1

        # 捕获图像
        img = sensor.snapshot()

        # 高性能检测最大的黑色三角形
        largest_triangle = find_largest_black_triangle_fast(img, triangle_detector)

        # 绘制检测结果
        draw_largest_black_triangle(img, largest_triangle)

        # 简化状态显示，减少字符串操作
        if largest_triangle is not None:
            status_color = (0, 255, 0)  # 绿色表示找到
            last_detection_frame = frame_count
            # 简化信息显示
            img.draw_string(10, 10, "FOUND!", color=status_color, scale=2)
            img.draw_string(10, 35, f"A:{largest_triangle.area()}", color=(255, 255, 255), scale=1)
        else:
            img.draw_string(10, 10, "Searching...", color=(255, 255, 0), scale=2)

        # 只显示关键信息，减少绘制操作
        img.draw_string(10, 55, f"FPS:{clock.fps():.0f}", color=(255, 255, 0), scale=1)

        # 显示图像
        Display.show_image(img)

        # 减少控制台输出频率，提升性能
        if largest_triangle is not None and frame_count % 30 == 0:  # 每30帧输出一次
            print(f"检测到黑色三角形 - 位置:({largest_triangle.cx()}, {largest_triangle.cy()}), 面积:{largest_triangle.area()}")

        # 移除延时，最大化刷新率
        # time.sleep_ms(30)  # 注释掉延时

# IDE中断释放资源代码
except KeyboardInterrupt as e:
    print("用户停止:", e)
except BaseException as e:
    print(f"程序异常: {e}")
finally:
    # 释放资源
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("K230D三角形识别程序已退出")
