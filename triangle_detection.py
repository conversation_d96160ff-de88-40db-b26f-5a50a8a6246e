#####################################################################################################
# @file         triangle_detection.py
# <AUTHOR> - Alex
# @version      V1.0
# @date         2025-01-30
# @brief        K230D三角形识别实验
# @license      Copyright (c) 2025, 米醋电子工作室
#####################################################################################################
# @attention
#
# 实验平台: 正点原子 K230D BOX开发板
# 功能说明: 使用摄像头实时识别三角形形状
# 识别方法: 基于轮廓检测和多边形逼近算法
#
#####################################################################################################

import time, os, sys
from media.sensor import *  # 导入sensor模块，使用摄像头相关接口
from media.display import * # 导入display模块，使用display相关接口
from media.media import *   # 导入media模块，使用meida相关接口
import image
import math

class TriangleDetector:
    """三角形检测器类"""
    
    def __init__(self):
        self.min_area = 500          # 最小三角形面积阈值
        self.max_area = 50000        # 最大三角形面积阈值
        self.approx_epsilon = 0.02   # 多边形逼近精度系数
        
    def is_triangle(self, contour):
        """
        判断轮廓是否为三角形
        @param contour: 轮廓点列表
        @return: True表示是三角形，False表示不是
        """
        if len(contour) < 3:
            return False
            
        # 计算轮廓面积
        area = self.calculate_area(contour)
        if area < self.min_area or area > self.max_area:
            return False
            
        # 使用多边形逼近
        approx_contour = self.approximate_polygon(contour)
        
        # 三角形应该有3个顶点
        if len(approx_contour) == 3:
            return True
            
        return False
    
    def calculate_area(self, contour):
        """
        计算轮廓面积（使用鞋带公式）
        @param contour: 轮廓点列表 [(x1,y1), (x2,y2), ...]
        @return: 面积值
        """
        if len(contour) < 3:
            return 0
            
        area = 0
        n = len(contour)
        for i in range(n):
            j = (i + 1) % n
            area += contour[i][0] * contour[j][1]
            area -= contour[j][0] * contour[i][1]
        return abs(area) / 2
    
    def approximate_polygon(self, contour):
        """
        多边形逼近算法（简化版Douglas-Peucker算法）
        @param contour: 原始轮廓点
        @return: 逼近后的多边形顶点
        """
        if len(contour) < 3:
            return contour
            
        # 计算周长
        perimeter = self.calculate_perimeter(contour)
        epsilon = self.approx_epsilon * perimeter
        
        # 简化的多边形逼近
        return self.douglas_peucker_simple(contour, epsilon)
    
    def calculate_perimeter(self, contour):
        """计算轮廓周长"""
        if len(contour) < 2:
            return 0
            
        perimeter = 0
        for i in range(len(contour)):
            j = (i + 1) % len(contour)
            dx = contour[j][0] - contour[i][0]
            dy = contour[j][1] - contour[i][1]
            perimeter += math.sqrt(dx*dx + dy*dy)
        return perimeter
    
    def douglas_peucker_simple(self, contour, epsilon):
        """简化版Douglas-Peucker算法"""
        if len(contour) <= 3:
            return contour
            
        # 找到距离起点和终点连线最远的点
        start = contour[0]
        end = contour[-1]
        max_dist = 0
        max_index = 0
        
        for i in range(1, len(contour) - 1):
            dist = self.point_to_line_distance(contour[i], start, end)
            if dist > max_dist:
                max_dist = dist
                max_index = i
        
        # 如果最大距离大于阈值，递归处理
        if max_dist > epsilon:
            # 递归处理两段
            left_part = self.douglas_peucker_simple(contour[:max_index+1], epsilon)
            right_part = self.douglas_peucker_simple(contour[max_index:], epsilon)
            # 合并结果（去除重复点）
            return left_part[:-1] + right_part
        else:
            # 距离小于阈值，直接返回起点和终点
            return [start, end]
    
    def point_to_line_distance(self, point, line_start, line_end):
        """计算点到直线的距离"""
        x0, y0 = point
        x1, y1 = line_start
        x2, y2 = line_end
        
        # 直线方程: (y2-y1)x - (x2-x1)y + x2*y1 - y2*x1 = 0
        # 点到直线距离公式
        numerator = abs((y2-y1)*x0 - (x2-x1)*y0 + x2*y1 - y2*x1)
        denominator = math.sqrt((y2-y1)**2 + (x2-x1)**2)
        
        if denominator == 0:
            return 0
        return numerator / denominator

def find_triangles_in_image(img, detector):
    """
    在图像中查找三角形
    @param img: 输入图像
    @param detector: 三角形检测器
    @return: 检测到的三角形列表
    """
    triangles = []
    
    # 转换为灰度图像
    gray_img = img.to_grayscale()
    
    # 边缘检测
    edges = gray_img.find_edges(image.EDGE_CANNY, threshold=(50, 100))
    
    # 查找轮廓
    contours = edges.find_contours(threshold=100)
    
    for contour in contours:
        # 将轮廓转换为点列表
        points = []
        for i in range(0, len(contour), 2):
            if i + 1 < len(contour):
                points.append((contour[i], contour[i+1]))
        
        # 检测是否为三角形
        if detector.is_triangle(points):
            triangles.append(points)
    
    return triangles

def draw_triangles(img, triangles):
    """
    在图像上绘制检测到的三角形
    @param img: 图像对象
    @param triangles: 三角形列表
    """
    for triangle in triangles:
        if len(triangle) >= 3:
            # 绘制三角形边框
            for i in range(len(triangle)):
                j = (i + 1) % len(triangle)
                img.draw_line(triangle[i][0], triangle[i][1], 
                             triangle[j][0], triangle[j][1], 
                             color=(255, 0, 0), thickness=3)
            
            # 在三角形中心绘制标记
            center_x = sum(p[0] for p in triangle) // len(triangle)
            center_y = sum(p[1] for p in triangle) // len(triangle)
            img.draw_circle(center_x, center_y, 5, color=(0, 255, 0), thickness=2, fill=True)
            img.draw_string(center_x-20, center_y-10, "Triangle", color=(0, 255, 0), scale=1)

# 主程序
try:
    # 初始化摄像头
    sensor = Sensor(width=1280, height=960)  # 构建摄像头对象
    sensor.reset()  # 复位和初始化摄像头
    sensor.set_framesize(Sensor.VGA)      # 设置帧大小VGA(640x480)
    sensor.set_pixformat(Sensor.RGB565)   # 设置输出图像格式
    
    # 初始化显示器
    Display.init(Display.ST7701, width=640, height=480, fps=90, to_ide=True)
    MediaManager.init()  # 初始化media资源管理器
    sensor.run()  # 启动sensor
    
    # 创建三角形检测器
    triangle_detector = TriangleDetector()
    
    print("K230D三角形识别程序启动成功！")
    print("将三角形物体放在摄像头前进行识别...")
    
    clock = time.clock()  # 构造clock对象用于计算FPS
    
    while True:
        os.exitpoint()  # 检测IDE中断
        clock.tick()    # 记录开始时间
        
        # 捕获图像
        img = sensor.snapshot()
        
        # 检测三角形
        triangles = find_triangles_in_image(img, triangle_detector)
        
        # 绘制检测结果
        draw_triangles(img, triangles)
        
        # 显示检测数量
        img.draw_string(10, 10, f"Triangles: {len(triangles)}", 
                       color=(255, 255, 0), scale=2)
        img.draw_string(10, 30, f"FPS: {clock.fps():.1f}", 
                       color=(255, 255, 0), scale=1)
        
        # 显示图像
        Display.show_image(img)
        
        if len(triangles) > 0:
            print(f"检测到 {len(triangles)} 个三角形")

# IDE中断释放资源代码
except KeyboardInterrupt as e:
    print("用户停止:", e)
except BaseException as e:
    print(f"程序异常: {e}")
finally:
    # 释放资源
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("K230D三角形识别程序已退出")
