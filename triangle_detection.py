#####################################################################################################
# @file         triangle_detection.py
# <AUTHOR> - Alex
# @version      V2.0
# @date         2025-01-30
# @brief        K230D三角形识别实验 - 兼容版本
# @license      Copyright (c) 2025, 米醋电子工作室
#####################################################################################################
# @attention
#
# 实验平台: 正点原子 K230D BOX开发板
# 功能说明: 使用摄像头实时识别三角形形状
# 识别方法: 基于K230D原生支持的图像处理方法
#
#####################################################################################################

import time, os, sys
from media.sensor import *  # 导入sensor模块，使用摄像头相关接口
from media.display import * # 导入display模块，使用display相关接口
from media.media import *   # 导入media模块，使用meida相关接口
import image
import math

class TriangleDetector:
    """黑色三角形检测器类 - 专门用于检测黑色三角形"""

    def __init__(self):
        self.min_area = 500          # 最小三角形面积阈值
        self.max_area = 30000        # 最大三角形面积阈值
        self.min_pixels = 80         # 最小像素数阈值（降低以检测更小的三角形）

    def is_triangle_blob(self, blob):
        """
        判断色块是否为三角形
        @param blob: 色块对象
        @return: True表示是三角形，False表示不是
        """
        # 检查面积
        if blob.pixels() < self.min_pixels:
            return False

        area = blob.area()
        if area < self.min_area or area > self.max_area:
            return False

        # 检查形状特征
        # 三角形的周长与面积比值有特定范围
        perimeter = blob.perimeter()
        if perimeter == 0:
            return False

        # 计算圆形度 (4π*面积)/(周长²)
        # 三角形的圆形度约为0.6-0.8
        roundness = (4 * math.pi * area) / (perimeter * perimeter)

        # 检查长宽比，三角形通常不会太扁
        w_h_ratio = blob.w() / blob.h() if blob.h() > 0 else 0
        h_w_ratio = blob.h() / blob.w() if blob.w() > 0 else 0
        aspect_ratio = max(w_h_ratio, h_w_ratio)

        # 三角形判断条件
        if (0.4 < roundness < 0.9 and aspect_ratio < 3.0):
            return True

        return False

    def analyze_shape_corners(self, blob):
        """
        分析形状的角点特征
        @param blob: 色块对象
        @return: 角点分析结果
        """
        # 使用色块的几何特征来判断
        # 三角形的特征：
        # 1. 有明显的角点
        # 2. 边数较少
        # 3. 不规则度适中

        # 计算密度 (像素数/外接矩形面积)
        rect_area = blob.w() * blob.h()
        density = blob.pixels() / rect_area if rect_area > 0 else 0

        # 三角形密度通常在0.4-0.7之间
        return 0.4 < density < 0.8

def find_largest_black_triangle(img, detector):
    """
    在图像中查找最大的黑色三角形
    @param img: 输入图像
    @param detector: 三角形检测器
    @return: 最大的黑色三角形色块，如果没有找到返回None
    """
    largest_triangle = None
    max_area = 0

    # 专门针对黑色物体的阈值设置
    # LAB颜色空间中，黑色物体的L值较低
    black_thresholds = [
        (0, 30, -128, 127, -128, 127),   # 深黑色
        (0, 40, -128, 127, -128, 127),   # 黑色
        (0, 50, -128, 127, -128, 127),   # 深灰色
    ]

    # 对每个黑色阈值进行检测
    for threshold in black_thresholds:
        try:
            # 查找黑色色块
            blobs = img.find_blobs([threshold], pixels_threshold=detector.min_pixels,
                                 area_threshold=detector.min_area, merge=True)

            for blob in blobs:
                # 检查是否为三角形
                if detector.is_triangle_blob(blob) and detector.analyze_shape_corners(blob):
                    # 找到面积最大的三角形
                    if blob.area() > max_area:
                        max_area = blob.area()
                        largest_triangle = blob

        except Exception as e:
            # 如果某个阈值检测失败，继续下一个
            continue

    return largest_triangle

def draw_largest_black_triangle(img, triangle_blob):
    """
    在图像上绘制最大的黑色三角形
    @param img: 图像对象
    @param triangle_blob: 最大的黑色三角形色块，可能为None
    """
    if triangle_blob is None:
        return

    # 绘制醒目的红色外接矩形
    img.draw_rectangle(triangle_blob.rect(), color=(255, 0, 0), thickness=3)

    # 绘制绿色中心点
    img.draw_circle(triangle_blob.cx(), triangle_blob.cy(), 8, color=(0, 255, 0), thickness=3, fill=True)

    # 绘制黄色标签
    label = "LARGEST BLACK TRIANGLE"
    img.draw_string(triangle_blob.x(), triangle_blob.y()-20, label, color=(255, 255, 0), scale=1)

    # 绘制详细信息
    info1 = f"Area: {triangle_blob.area()}"
    info2 = f"Center: ({triangle_blob.cx()}, {triangle_blob.cy()})"
    info3 = f"Size: {triangle_blob.w()}x{triangle_blob.h()}"

    img.draw_string(triangle_blob.x(), triangle_blob.y()+triangle_blob.h()+5, info1, color=(0, 255, 255), scale=1)
    img.draw_string(triangle_blob.x(), triangle_blob.y()+triangle_blob.h()+20, info2, color=(0, 255, 255), scale=1)
    img.draw_string(triangle_blob.x(), triangle_blob.y()+triangle_blob.h()+35, info3, color=(0, 255, 255), scale=1)

# 主程序
try:
    # 初始化摄像头
    sensor = Sensor(width=1280, height=960)  # 构建摄像头对象
    sensor.reset()  # 复位和初始化摄像头
    sensor.set_framesize(Sensor.VGA)      # 设置帧大小VGA(640x480)
    sensor.set_pixformat(Sensor.RGB565)   # 设置输出图像格式
    
    # 初始化显示器
    Display.init(Display.ST7701, width=640, height=480, fps=90, to_ide=True)
    MediaManager.init()  # 初始化media资源管理器
    sensor.run()  # 启动sensor
    
    # 创建三角形检测器
    triangle_detector = TriangleDetector()

    print("K230D最大黑色三角形识别程序启动成功！")
    print("将黑色三角形物体放在摄像头前进行识别...")
    print("程序将自动找到并标记最大的黑色三角形")
    print("提示：确保三角形与背景有足够的对比度")

    clock = time.clock()  # 构造clock对象用于计算FPS
    frame_count = 0
    last_detection_frame = 0

    while True:
        os.exitpoint()  # 检测IDE中断
        clock.tick()    # 记录开始时间
        frame_count += 1

        # 捕获图像
        img = sensor.snapshot()

        # 检测最大的黑色三角形
        largest_triangle = find_largest_black_triangle(img, triangle_detector)

        # 绘制检测结果
        draw_largest_black_triangle(img, largest_triangle)

        # 显示检测状态
        if largest_triangle is not None:
            status = "BLACK TRIANGLE FOUND!"
            status_color = (0, 255, 0)  # 绿色表示找到
            area_info = f"Area: {largest_triangle.area()}"
            last_detection_frame = frame_count
        else:
            status = "Searching for black triangle..."
            status_color = (255, 255, 0)  # 黄色表示搜索中
            area_info = "No triangle detected"

        # 显示状态信息
        img.draw_string(10, 10, status, color=status_color, scale=2)
        img.draw_string(10, 35, area_info, color=(255, 255, 255), scale=1)
        img.draw_string(10, 55, f"FPS: {clock.fps():.1f}", color=(255, 255, 0), scale=1)
        img.draw_string(10, 75, f"Frame: {frame_count}", color=(255, 255, 0), scale=1)

        # 显示图像
        Display.show_image(img)

        # 检测到三角形时输出详细信息
        if largest_triangle is not None and (frame_count - last_detection_frame) < 2:
            print(f"Frame {frame_count}: 找到最大黑色三角形!")
            print(f"  位置: ({largest_triangle.cx()}, {largest_triangle.cy()})")
            print(f"  面积: {largest_triangle.area()}")
            print(f"  尺寸: {largest_triangle.w()}x{largest_triangle.h()}")
            print(f"  像素数: {largest_triangle.pixels()}")

        # 短暂延时，避免过快刷新
        time.sleep_ms(30)

# IDE中断释放资源代码
except KeyboardInterrupt as e:
    print("用户停止:", e)
except BaseException as e:
    print(f"程序异常: {e}")
finally:
    # 释放资源
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("K230D三角形识别程序已退出")
